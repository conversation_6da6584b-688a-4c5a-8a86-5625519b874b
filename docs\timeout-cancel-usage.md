# EasyQuery 双重超时保护机制

## 概述

基于达梦数据库JDBC查询超时设置方法的性能对比，实现了双重保护的超时控制机制：

1. **第一层保护**：客户端 Future 超时控制（主要机制）
2. **第二层保护**：数据库 setQueryTimeout（备用机制）

## 实现原理

### 客户端超时控制
```java
// 使用客户端超时控制
Future<ResultSet> future = executor.submit(() -> stmt.executeQuery(sql));
try {
    ResultSet rs = future.get(10, TimeUnit.SECONDS);
} catch (TimeoutException e) {
    future.cancel(true);  // 中断线程
    throw new SQLTimeoutException("客户端强制终止");
}
```

### 双重保护机制
- **客户端控制**：使用 `Future.get(timeout, TimeUnit.SECONDS)`
- **数据库控制**：保留 `stmt.setQueryTimeout(timeout+2)`
- **优先级**：客户端超时优先触发，数据库超时作为备用

## 性能优势

相比纯 setQueryTimeout 方案：
- ✅ 网络开销：低
- ✅ 服务器负载：低  
- ✅ 连接影响：不影响
- ✅ 推荐指数：★★★★★

## 使用方法

### 基本使用
```java
EasyQueryImpl query = new EasyQueryImpl("datasourceName");
query.setTimeout(30); // 设置30秒超时

try {
    // 自动应用双重超时保护
    List<EasyRow> results = query.queryForList("SELECT * FROM large_table");
    System.out.println("查询完成");
} catch (SQLTimeoutException e) {
    System.out.println("查询超时: " + e.getMessage());
}
```

### 超时设置
```java
// 设置超时时间
query.setTimeout(60); // 60秒

// 实际超时机制：
// - 客户端超时：60秒（主要）
// - 数据库超时：62秒（备用）
```

## 适用场景

- 长时间运行的查询需要精确超时控制
- 需要降低网络开销和服务器负载
- 要求不影响数据库连接的超时处理
- 批量数据处理中的超时控制

## 技术细节

### 线程池配置
- 使用 `Executors.newCachedThreadPool()` 
- 自动管理线程生命周期
- 支持并发查询

### 异常处理
- `TimeoutException` → `SQLTimeoutException`
- 保持原有异常处理逻辑
- 自动资源清理

### 兼容性
- 完全向后兼容
- 不影响现有API
- 保持原有日志记录
