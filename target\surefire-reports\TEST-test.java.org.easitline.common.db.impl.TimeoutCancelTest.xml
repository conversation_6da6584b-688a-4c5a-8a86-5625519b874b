<?xml version="1.0" encoding="UTF-8"?>
<testsuite xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="https://maven.apache.org/surefire/maven-surefire-plugin/xsd/surefire-test-report-3.0.xsd" version="3.0" name="test.java.org.easitline.common.db.impl.TimeoutCancelTest" time="0.01" tests="0" errors="0" skipped="0" failures="0">
  <properties>
    <property name="sun.desktop" value="windows"/>
    <property name="awt.toolkit" value="sun.awt.windows.WToolkit"/>
    <property name="file.encoding.pkg" value="sun.io"/>
    <property name="java.specification.version" value="1.8"/>
    <property name="sun.cpu.isalist" value="amd64"/>
    <property name="sun.jnu.encoding" value="GBK"/>
    <property name="java.class.path" value="D:\develop\workbench202411\easitline-db\target\test-classes;D:\develop\workbench202411\easitline-db\target\classes;D:\develop\maven\repository\com\yunqu\mars\easitline-core\3.5\easitline-core-3.5.jar;D:\develop\maven\repository\com\yunqu\mars\easitline-utils\3.5\easitline-utils-3.5.jar;D:\develop\maven\repository\com\jfinal\enjoy\5.2.2\enjoy-5.2.2.jar;D:\develop\maven\repository\javax\servlet\javax.servlet-api\3.1.0\javax.servlet-api-3.1.0.jar;D:\develop\maven\repository\javax\servlet\jsp\jsp-api\2.2\jsp-api-2.2.jar;D:\develop\maven\repository\javax\websocket\javax.websocket-api\1.1\javax.websocket-api-1.1.jar;D:\develop\maven\repository\com\alibaba\druid\1.2.23\druid-1.2.23.jar;D:\develop\maven\repository\com\mysql\mysql-connector-j\8.4.0\mysql-connector-j-8.4.0.jar;D:\develop\maven\repository\com\google\protobuf\protobuf-java\3.25.1\protobuf-java-3.25.1.jar;D:\develop\maven\repository\org\quartz-scheduler\quartz\2.3.2\quartz-2.3.2.jar;D:\develop\maven\repository\com\mchange\c3p0\0.9.5.4\c3p0-0.9.5.4.jar;D:\develop\maven\repository\com\mchange\mchange-commons-java\0.2.15\mchange-commons-java-0.2.15.jar;D:\develop\maven\repository\com\zaxxer\HikariCP-java7\2.4.13\HikariCP-java7-2.4.13.jar;D:\develop\maven\repository\org\quartz-scheduler\quartz-jobs\2.3.2\quartz-jobs-2.3.2.jar;D:\develop\maven\repository\org\apache\commons\commons-lang3\3.12.0\commons-lang3-3.12.0.jar;D:\develop\maven\repository\org\apache\commons\commons-collections4\4.4\commons-collections4-4.4.jar;D:\develop\maven\repository\commons-collections\commons-collections\3.2.2\commons-collections-3.2.2.jar;D:\develop\maven\repository\org\apache\commons\commons-jexl\2.1.1\commons-jexl-2.1.1.jar;D:\develop\maven\repository\commons-logging\commons-logging\1.2\commons-logging-1.2.jar;D:\develop\maven\repository\org\apache\commons\commons-csv\1.8\commons-csv-1.8.jar;D:\develop\maven\repository\org\apache\commons\commons-pool2\2.12.1\commons-pool2-2.12.1.jar;D:\develop\maven\repository\commons-io\commons-io\2.14.0\commons-io-2.14.0.jar;D:\develop\maven\repository\commons-codec\commons-codec\1.16.0\commons-codec-1.16.0.jar;D:\develop\maven\repository\commons-fileupload\commons-fileupload\1.5\commons-fileupload-1.5.jar;D:\develop\maven\repository\org\jsoup\jsoup\1.14.3\jsoup-1.14.3.jar;D:\develop\maven\repository\com\alibaba\fastjson\1.2.83\fastjson-1.2.83.jar;D:\develop\maven\repository\log4j\log4j\1.2.17\log4j-1.2.17.jar;D:\develop\maven\repository\org\slf4j\slf4j-api\1.7.29\slf4j-api-1.7.29.jar;D:\develop\maven\repository\redis\clients\jedis\3.10.0\jedis-3.10.0.jar;D:\develop\maven\repository\org\apache\httpcomponents\httpclient\4.5.14\httpclient-4.5.14.jar;D:\develop\maven\repository\org\apache\httpcomponents\httpcore\4.4.16\httpcore-4.4.16.jar;D:\develop\maven\repository\org\dom4j\dom4j\2.1.3\dom4j-2.1.3.jar;D:\develop\maven\repository\cn\hutool\hutool-core\5.8.33\hutool-core-5.8.33.jar;D:\develop\maven\repository\com\google\guava\guava\32.1.3-jre\guava-32.1.3-jre.jar;D:\develop\maven\repository\com\google\guava\failureaccess\1.0.1\failureaccess-1.0.1.jar;D:\develop\maven\repository\com\google\guava\listenablefuture\9999.0-empty-to-avoid-conflict-with-guava\listenablefuture-9999.0-empty-to-avoid-conflict-with-guava.jar;D:\develop\maven\repository\com\google\code\findbugs\jsr305\3.0.2\jsr305-3.0.2.jar;D:\develop\maven\repository\org\checkerframework\checker-qual\3.37.0\checker-qual-3.37.0.jar;D:\develop\maven\repository\com\google\errorprone\error_prone_annotations\2.21.1\error_prone_annotations-2.21.1.jar;D:\develop\maven\repository\com\google\j2objc\j2objc-annotations\2.8\j2objc-annotations-2.8.jar;D:\develop\maven\repository\org\apache\activemq\activemq-all\5.15.11\activemq-all-5.15.11.jar;D:\develop\maven\repository\joda-time\joda-time\2.12.6\joda-time-2.12.6.jar;"/>
    <property name="java.vm.vendor" value="Amazon.com Inc."/>
    <property name="sun.arch.data.model" value="64"/>
    <property name="user.variant" value=""/>
    <property name="java.vendor.url" value="https://aws.amazon.com/corretto/"/>
    <property name="user.timezone" value="Asia/Shanghai"/>
    <property name="java.vm.specification.version" value="1.8"/>
    <property name="os.name" value="Windows 11"/>
    <property name="user.country" value="CN"/>
    <property name="sun.java.launcher" value="SUN_STANDARD"/>
    <property name="sun.boot.library.path" value="D:\develop\jdks\corretto-1.8.0_432\jre\bin"/>
    <property name="sun.java.command" value="C:\Users\<USER>\AppData\Local\Temp\surefire2742319451743222443\surefirebooter-20250806175409093_3.jar C:\Users\<USER>\AppData\Local\Temp\surefire2742319451743222443 2025-08-06T17-54-08_139-jvmRun1 surefire-20250806175409093_1tmp surefire_0-20250806175409093_2tmp"/>
    <property name="surefire.test.class.path" value="D:\develop\workbench202411\easitline-db\target\test-classes;D:\develop\workbench202411\easitline-db\target\classes;D:\develop\maven\repository\com\yunqu\mars\easitline-core\3.5\easitline-core-3.5.jar;D:\develop\maven\repository\com\yunqu\mars\easitline-utils\3.5\easitline-utils-3.5.jar;D:\develop\maven\repository\com\jfinal\enjoy\5.2.2\enjoy-5.2.2.jar;D:\develop\maven\repository\javax\servlet\javax.servlet-api\3.1.0\javax.servlet-api-3.1.0.jar;D:\develop\maven\repository\javax\servlet\jsp\jsp-api\2.2\jsp-api-2.2.jar;D:\develop\maven\repository\javax\websocket\javax.websocket-api\1.1\javax.websocket-api-1.1.jar;D:\develop\maven\repository\com\alibaba\druid\1.2.23\druid-1.2.23.jar;D:\develop\maven\repository\com\mysql\mysql-connector-j\8.4.0\mysql-connector-j-8.4.0.jar;D:\develop\maven\repository\com\google\protobuf\protobuf-java\3.25.1\protobuf-java-3.25.1.jar;D:\develop\maven\repository\org\quartz-scheduler\quartz\2.3.2\quartz-2.3.2.jar;D:\develop\maven\repository\com\mchange\c3p0\0.9.5.4\c3p0-0.9.5.4.jar;D:\develop\maven\repository\com\mchange\mchange-commons-java\0.2.15\mchange-commons-java-0.2.15.jar;D:\develop\maven\repository\com\zaxxer\HikariCP-java7\2.4.13\HikariCP-java7-2.4.13.jar;D:\develop\maven\repository\org\quartz-scheduler\quartz-jobs\2.3.2\quartz-jobs-2.3.2.jar;D:\develop\maven\repository\org\apache\commons\commons-lang3\3.12.0\commons-lang3-3.12.0.jar;D:\develop\maven\repository\org\apache\commons\commons-collections4\4.4\commons-collections4-4.4.jar;D:\develop\maven\repository\commons-collections\commons-collections\3.2.2\commons-collections-3.2.2.jar;D:\develop\maven\repository\org\apache\commons\commons-jexl\2.1.1\commons-jexl-2.1.1.jar;D:\develop\maven\repository\commons-logging\commons-logging\1.2\commons-logging-1.2.jar;D:\develop\maven\repository\org\apache\commons\commons-csv\1.8\commons-csv-1.8.jar;D:\develop\maven\repository\org\apache\commons\commons-pool2\2.12.1\commons-pool2-2.12.1.jar;D:\develop\maven\repository\commons-io\commons-io\2.14.0\commons-io-2.14.0.jar;D:\develop\maven\repository\commons-codec\commons-codec\1.16.0\commons-codec-1.16.0.jar;D:\develop\maven\repository\commons-fileupload\commons-fileupload\1.5\commons-fileupload-1.5.jar;D:\develop\maven\repository\org\jsoup\jsoup\1.14.3\jsoup-1.14.3.jar;D:\develop\maven\repository\com\alibaba\fastjson\1.2.83\fastjson-1.2.83.jar;D:\develop\maven\repository\log4j\log4j\1.2.17\log4j-1.2.17.jar;D:\develop\maven\repository\org\slf4j\slf4j-api\1.7.29\slf4j-api-1.7.29.jar;D:\develop\maven\repository\redis\clients\jedis\3.10.0\jedis-3.10.0.jar;D:\develop\maven\repository\org\apache\httpcomponents\httpclient\4.5.14\httpclient-4.5.14.jar;D:\develop\maven\repository\org\apache\httpcomponents\httpcore\4.4.16\httpcore-4.4.16.jar;D:\develop\maven\repository\org\dom4j\dom4j\2.1.3\dom4j-2.1.3.jar;D:\develop\maven\repository\cn\hutool\hutool-core\5.8.33\hutool-core-5.8.33.jar;D:\develop\maven\repository\com\google\guava\guava\32.1.3-jre\guava-32.1.3-jre.jar;D:\develop\maven\repository\com\google\guava\failureaccess\1.0.1\failureaccess-1.0.1.jar;D:\develop\maven\repository\com\google\guava\listenablefuture\9999.0-empty-to-avoid-conflict-with-guava\listenablefuture-9999.0-empty-to-avoid-conflict-with-guava.jar;D:\develop\maven\repository\com\google\code\findbugs\jsr305\3.0.2\jsr305-3.0.2.jar;D:\develop\maven\repository\org\checkerframework\checker-qual\3.37.0\checker-qual-3.37.0.jar;D:\develop\maven\repository\com\google\errorprone\error_prone_annotations\2.21.1\error_prone_annotations-2.21.1.jar;D:\develop\maven\repository\com\google\j2objc\j2objc-annotations\2.8\j2objc-annotations-2.8.jar;D:\develop\maven\repository\org\apache\activemq\activemq-all\5.15.11\activemq-all-5.15.11.jar;D:\develop\maven\repository\joda-time\joda-time\2.12.6\joda-time-2.12.6.jar;"/>
    <property name="sun.cpu.endian" value="little"/>
    <property name="user.home" value="C:\Users\<USER>\develop\jdks\corretto-1.8.0_432\jre"/>
    <property name="basedir" value="D:\develop\workbench202411\easitline-db"/>
    <property name="file.separator" value="\"/>
    <property name="line.separator" value="&#10;"/>
    <property name="java.vm.specification.vendor" value="Oracle Corporation"/>
    <property name="java.specification.name" value="Java Platform API Specification"/>
    <property name="java.awt.graphicsenv" value="sun.awt.Win32GraphicsEnvironment"/>
    <property name="surefire.real.class.path" value="C:\Users\<USER>\AppData\Local\Temp\surefire2742319451743222443\surefirebooter-20250806175409093_3.jar"/>
    <property name="sun.boot.class.path" value="D:\develop\jdks\corretto-1.8.0_432\jre\lib\resources.jar;D:\develop\jdks\corretto-1.8.0_432\jre\lib\rt.jar;D:\develop\jdks\corretto-1.8.0_432\jre\lib\sunrsasign.jar;D:\develop\jdks\corretto-1.8.0_432\jre\lib\jsse.jar;D:\develop\jdks\corretto-1.8.0_432\jre\lib\jce.jar;D:\develop\jdks\corretto-1.8.0_432\jre\lib\charsets.jar;D:\develop\jdks\corretto-1.8.0_432\jre\lib\jfr.jar;D:\develop\jdks\corretto-1.8.0_432\jre\classes"/>
    <property name="user.script" value=""/>
    <property name="sun.management.compiler" value="HotSpot 64-Bit Tiered Compilers"/>
    <property name="java.runtime.version" value="1.8.0_432-b06"/>
    <property name="user.name" value="46419"/>
    <property name="path.separator" value=";"/>
    <property name="os.version" value="10.0"/>
    <property name="java.endorsed.dirs" value="D:\develop\jdks\corretto-1.8.0_432\jre\lib\endorsed"/>
    <property name="java.runtime.name" value="OpenJDK Runtime Environment"/>
    <property name="file.encoding" value="GBK"/>
    <property name="java.vm.name" value="OpenJDK 64-Bit Server VM"/>
    <property name="localRepository" value="D:\develop\maven\repository"/>
    <property name="java.vendor.url.bug" value="https://github.com/corretto/corretto-8/issues/"/>
    <property name="java.io.tmpdir" value="C:\Users\<USER>\AppData\Local\Temp\"/>
    <property name="java.version" value="1.8.0_432"/>
    <property name="user.dir" value="D:\develop\workbench202411\easitline-db"/>
    <property name="os.arch" value="amd64"/>
    <property name="java.vm.specification.name" value="Java Virtual Machine Specification"/>
    <property name="java.awt.printerjob" value="sun.awt.windows.WPrinterJob"/>
    <property name="sun.os.patch.level" value=""/>
    <property name="java.library.path" value="D:\develop\jdks\corretto-1.8.0_432\jre\bin;C:\WINDOWS\Sun\Java\bin;C:\WINDOWS\system32;C:\WINDOWS;C:\Users\<USER>\AppData\Local\fnm_multishells\29036_1754474035619;C:\Program Files\WindowsApps\Microsoft.PowerShell_7.5.2.0_x64__8wekyb3d8bbwe;d:\soft\AI\cursor\resources\app\bin;D:\soft\AI\ShadowBot;d:\soft\AI\cursor\resources\app\bin;D:\soft\develop\Python311\Scripts\;D:\soft\develop\Python311\;C:\WINDOWS\system32;C:\WINDOWS;C:\WINDOWS\System32\Wbem;C:\WINDOWS\System32\WindowsPowerShell\v1.0\;C:\WINDOWS\System32\OpenSSH\;C:\Program Files\OpenVPN\bin;C:\Program Files\dotnet\;D:\soft\develop\TortoiseSVN\bin;D:\develop\Git\cmd;D:\soft\develop\Sniffnet\;D:\develop\maven\apache-maven-3.9.6\bin;D:\soft\develop\Git\cmd;C:\Program Files\Zero Install;D:\develop\jdks\corretto-1.8.0_432\bin;D:\develop\jdks\corretto-1.8.0_432\jre\bin;D:\develop\gradle\gradle-8.11.1\bin;C:\WINDOWS\system32;C:\WINDOWS;C:\WINDOWS\System32\Wbem;C:\WINDOWS\System32\WindowsPowerShell\v1.0\;C:\WINDOWS\System32\OpenSSH\;c:\Users\<USER>\AppData\Local\Programs\cursor\resources\app\bin;D:\soft\develop\寰俊web寮�鍙戣�呭伐鍏穃dll;D:\soft\develop\???web?????????\dll;C:\Users\<USER>\AppData\Local\Microsoft\WindowsApps;D:\soft\develop\Fiddler;C:\Users\<USER>\AppData\Roaming\npm;D:\soft\de;C:\Program Files\AskLink;D:\soft\develop\TortoiseGit\bin;D:\soft\AI\Void\bin;D:\develop\maven\maven-mvnd\bin;D:\soft\AI\cunzhi\cunzhi.exe;D:\soft\AI\cunzhi\stop.exe;C:\Users\<USER>\AppData\Local\Programs\oh-my-posh\bin\;D:\scoop\shims;C:\Users\<USER>\.cargo\bin;C:\Users\<USER>\.local\bin;D:\soft\develop\nodejs\;D:\soft\develop\PyCharm\bin;D:\soft\develop\Microsoft VS Code\bin;C:\Users\<USER>\AppData\Local\Programs\Ollama;D:\develop\JetBrains\IntelliJ IDEA 2024.3\bin;C:\Users\<USER>\soft\develop\nodejs-config\node_global;C:\Users\<USER>\.bun\bin;D:\soft\AI\cursor\resources\app\bin;D:\soft\AI\Lingma\bin;C:\Users\<USER>\AppData\Local\UGit\app-5.34.0\resources\app\git\cmd;C:\Users\<USER>\AppData\Local\UGit\bin;D:\soft\AI\Kiro\bin;C:\Users\<USER>\AppData\Local\Microsoft\WinGet\Packages\zufuliu.notepad4_Microsoft.Winget.Source_8wekyb3d8bbwe;C:\Users\<USER>\AppData\Local\Microsoft\WinGet\Packages\Schniz.fnm_Microsoft.Winget.Source_8wekyb3d8bbwe;D:\soft\AI\CodeBuddy\bin;."/>
    <property name="java.vm.info" value="mixed mode"/>
    <property name="java.vendor" value="Amazon.com Inc."/>
    <property name="java.vm.version" value="25.432-b06"/>
    <property name="java.specification.maintenance.version" value="6"/>
    <property name="java.ext.dirs" value="D:\develop\jdks\corretto-1.8.0_432\jre\lib\ext;C:\WINDOWS\Sun\Java\lib\ext"/>
    <property name="sun.io.unicode.encoding" value="UnicodeLittle"/>
    <property name="java.class.version" value="52.0"/>
  </properties>
</testsuite>